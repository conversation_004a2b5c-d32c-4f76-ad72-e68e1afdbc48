# Go 编译产物
*.exe                 # Windows 可执行文件
*.dll                 # Windows 动态链接库
*.so                  # Linux/macOS 共享库
*.dylib               # macOS 动态库
*.test                # Go 测试缓存
*.prof                # Go 性能分析文件

# Go 模块相关
go.sum                # 模块校验和文件（如果团队共享，可以考虑不忽略）
vendor/               # Go 模块的本地副本（如果团队共享，可以考虑不忽略）

# GoLand IDE 特定文件
.idea/                # GoLand 项目配置目录
*.iml                 # GoLand 模块文件
*.ipr                 # GoLand 项目文件
*.iws                 # GoLand 工作区文件
out/                  # 编译输出目录，GoLand 可能会生成
.DS_Store             # macOS 隐藏文件

# 依赖管理工具
# 如果使用 vendoring，则 vendor/ 目录通常需要被版本控制。
# 如果不使用 vendoring 而是每次构建都下载依赖，则可以忽略 vendor/。
# vendoring 是 Go 早期常用的方式，现在 Go Modules 更常见。
# 如果团队约定不提交 vendor/，可以取消注释下面一行
# vendor/

# 编译和运行时文件
bin/                  # 编译后的可执行文件目录
pkg/                  # 编译后的包文件目录（Go 1.10 之前常见，现在多在 go build 时直接生成）
*.log                 # 日志文件
tmp/                  # 临时文件

# 包管理工具（如果使用其他）
# dep
Gopkg.lock
Gopkg.toml

# glide
glide.lock

# 其他常见文件
.env                  # 环境变量文件
.env.*                # 其他环境变量文件
*.pem                 # 私钥文件
*.key                 # 密钥文件
*.crt                 # 证书文件
*.jks                 # Java KeyStore (有时会出现在Go项目中，比如某些Java-Go互操作)
*.p12                 # PKCS#12 (有时会出现在Go项目中)
*.keystore            # 密钥存储
node_modules/         # 如果你的Go项目也包含前端代码
coverage.out          # Go 测试覆盖率文件
profile.pprof         # Go CPU/Memory 性能分析文件

# macOS 特定
.DS_Store

# Windows 特定
Thumbs.db
